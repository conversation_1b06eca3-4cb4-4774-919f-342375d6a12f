import 'package:dio/dio.dart';

import '/src/core/config/app_strings.dart';
import '/src/core/network/dio_client.dart';

import '/src/core/services/exceptions.dart';
import '/src/data/repository/get_access_token_repository_impl.dart';
import '/src/domain/models/info_card_item_model.dart';
import '/src/domain/models/user.dart';
import '/src/domain/repository/get_access_token_repository.dart';
import '/src/domain/repository/get_infocard_repository.dart';

import '../../core/network/api_config.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/services/flutter_secure_storage.dart';

class GetInfocardRepositoryImpl implements GetInfocardRepository {
  GetInfocardRepositoryImpl();
  static const String baseUrl = APIConfig.baseUrl;
  final SessionManager _sessionManager = SessionManager();
  final GetAccessTokenRepository _getAccessTokenRepository =
      GetAccessTokenRepositoryImpl();

  Future<InfoCardItemModel> getInfocardItemValue(
    String url,
    String type,
  ) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.get(url, queryParameters: {'type': type});

      if (response.statusCode == 200) {
        return InfoCardItemModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
