import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/presentation/cubit/filter/filter_cubit.dart';
import '../../../core/enum/user_status.dart';
import '../../../core/utils/column_mapping_utils.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../../domain/models/user.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/date_formatter.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/domain/models/agent_model.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsListScreen extends HookWidget {
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;

  const AgentsListScreen({
    super.key,
    required this.onNavigateToAgentNetworkAgent,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getAgentBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    final filterCubit = context.read<FilterCubit>();
    final user = context.watch<UserCubit>().state.user;
    final sortColumn = useState<String>('');
    final sortOrder = useState<String>('');
    final ValueNotifier<String?> searchString = useState(null);
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalElements = useState(0);
    final brokerageFilterOptions = useState<List<TableFilter>>([]);
    final agentFilterOptions = useState<List<TableFilter>>([]);
    final currentFilters = useState<Map<String, dynamic>>({});
    //state values for filter
    final relatedBrokerage = useState<String>('');
    final agentNameFilter = useState<String>('');

    // Add this useEffect to fetch initial data
    useEffect(() {
      Future.microtask(() async {
        if (context.mounted && user != null) {
          _fetchAgents(
            context,
            user,
            selectedDate: selectedDate.value,
            sortBy: sortByDefault,
            page: 0,
            searchString: searchString.value ?? "",
          );
        }
        await filterCubit.getBrokerageFilterOptions();
        FilterState state = filterCubit.state;
        if (state is FilterLoaded) {
          brokerageFilterOptions.value = state.filterOptions;
        }
        // await filterCubit.getAgentFilterOptions();
        // state = filterCubit.state;
        // if (state is FilterLoaded) {
        //   agentFilterOptions.value = state.filterOptions;
        // }
      });
      return null;
    }, [user?.userId]);

    // Use predefined headers from app_strings.dart (excluding agentStatus for now)
    final List<String> formattedHeaders = [
      agentName,
      agentContact,
      agentEmail,
      agentJoinDate,
      agentState,
      agentCity,
      agentLevel,
      agentRelatedBrokerage,
      agentRefferedBy,
      agentDirectRecruits,
      agentTotalSales,
      agentTotalRevenue,
      agentCommission,
      agentStatus,
      // agentStatus, // Commented out as it's not in API yet
    ];
    // Get user from UserCubit
    final userCubit = context.read<UserCubit>().state;

    if (userCubit is! UserLoaded) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }

    return BlocConsumer<AgentCubit, AgentState>(
      listener: (BuildContext context, AgentState state) {
        if (state is AgentLoaded) {
          pageCount.value = state.totalPages;
          totalElements.value = state.totalCount;
        }
      },
      builder: (context, state) {
        List<AgentModel> agentData = [];
        String? errorMessage;
        if (state is AgentLoaded) {
          agentData = state.agents;
        } else if (state is AgentError) {
          errorMessage = state.message;
        }

        void handleSort(String columnName, bool ascending) async {
          String backendColumnName = _getBackendColumnName(columnName);
          sortColumn.value = columnName;
          sortOrder.value = ascending ? 'ASC' : 'DESC';
          if (columnName == agentDirectRecruits) {
            return;
          }
          if (context.mounted) {
            await _fetchAgents(
              context,
              user,
              selectedDate: selectedDate.value,
              sortBy: backendColumnName,
              sortDirection: sortOrder.value,
              searchString: searchString.value ?? "",
              page: currentpage.value,
            );
          }
        }

        if (errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? Icons.lock_outline
                      : Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? authenticationRequired
                      : errorLoadingData,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? pleaseLoginToAccessAgentData
                      : '$errorPrefix$errorMessage',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    _fetchAgents(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      sortBy: sortByDefault,
                      page: currentpage.value,
                      searchString: searchString.value ?? "",
                    );
                  },
                  child: const Text(retry),
                ),
              ],
            ),
          );
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: CustomDataTableWidget<AgentModel>(
                data: agentData,
                title: agents,
                titleIcon: "$iconAssetpath/user.png",
                searchHint: searchAgent,
                searchFn: (agent) =>
                    agent.fullName +
                    agent.phone +
                    agent.email +
                    agent.state +
                    agent.city +
                    agent.depthLevel +
                    AppDateFormatter.formatJoiningDate(agent.joiningDate) +
                    agent.recruiterName +
                    agent.totalDownlineAgents.toString() +
                    agent.totalSales.toString() +
                    agent.commission.toString() +
                    agent.isActive.toString(),

                // Dynamic filtering system
                filterColumnNames: [
                  agentRelatedBrokerage, agentName, // name
                  // agentStatus, // status // dynamic filter from API // TODO: uncomment when API is ready
                ],
                filterValueExtractors: {
                  agentRelatedBrokerage: (agent) => agent.associatedBrokerage,
                  agentName: (agent) => agent.fullName,

                  // agentStatus: (agent) => agent.status.toString(), // TODO: uncomment when API is ready
                },
                // Use filterData and brokerFilterData for dropdown options in the filter section, mapping id to value
                filterOptions: {
                  if (brokerageFilterOptions != null)
                    agentRelatedBrokerage: brokerageFilterOptions.value,
                  if (agentFilterOptions != null)
                    agentName: agentFilterOptions.value,
                },
                filterOnChangeConfig: {
                  agentRelatedBrokerage:
                      true, // enable listener for this filter
                  agentName: false // no listener for this filter
                },
                onFilterChange: (filterKey, selectedValue) async {
                  // If the filterKey is agentRelatedBrokerage, fetch agent filter options with selectedValue
                  if (filterKey == agentRelatedBrokerage) {
                    await filterCubit.getAgentFilterOptions(
                      userId: user?.userId,
                      selectedValueParam: selectedValue,
                    );
                    FilterState state = filterCubit.state;
                    if (state is FilterLoaded) {
                      agentFilterOptions.value = state.filterOptions;
                    }
                  }
                },
                columnNames: formattedHeaders,
                cellBuilders: [
                  (agent) => agent.fullName, // name
                  (agent) => agent.phone, // contact
                  (agent) => agent.email, // email
                  (agent) => AppDateFormatter.formatDateMMddyyyy(
                    agent.joiningDate,
                  ), // joinDate
                  (agent) => agent.state, // state
                  (agent) => agent.city, // city
                  (agent) => agent.depthLevel, // level
                  (agent) => agent.associatedBrokerage, //associate broker
                  (agent) => agent.recruiterName, // referredBy - MISSING!
                  (agent) => agent.totalDownlineAgents
                      .toString(), // totalAgents - MISSING!
                  (agent) => agent.totalSales.toString(), // totalSales
                  (agent) =>
                      '$currencySymbol${agent.totalRevenue.toStringAsFixed(2)}', // total revenue
                  (agent) =>
                      '$currencySymbol${agent.commission.toStringAsFixed(2)}', // commission
                  (agent) => agent.isActive
                      ? UserStatus.active.value
                      : UserStatus.inactive.value,
                ],
                iconCellBuilders: [
                  (agent) => TableCellData(
                    text: agent.fullName,
                    leftIconAsset: "$iconAssetpath/agent_round.png",
                    iconSize: 30,
                  ),
                  null, // contact
                  null, // email
                  null, // joinDate
                  null, // state
                  null, // city
                  null, // level
                  null, // associatedBroker
                  null, // referredBy
                  null, // Direct recruites
                  null,
                  null, //total revenue
                  null, // totalSales
                  null, // commission
                  null, // status
                ],
                useIconBuilders: [
                  true, // name - use icon
                  false, // contact
                  false, // email
                  false, // joinDate
                  false, // state
                  false, // city
                  false, // level
                  false, //associatedBroker
                  false, // referredBy
                  false, // totalAgents
                  false,
                  false, // totalSales
                  false, // commission
                  false, // status
                ],
                // Widget builders for styled cells
                widgetCellBuilders: [
                  null, // name - use text
                  null, // contact - use text
                  null, // email - use text
                  null, // joinDate - use text
                  null, // state - use text
                  null, // city - use text
                  null, // level - use text
                  null, //
                  null,
                  null,
                  null, //
                  null,
                  null,
                  (context, agent) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: agent.isActive
                          ? AppTheme.agentStatusActiveBg.withAlpha(36)
                          : AppTheme.agentStatusInactiveBg.withAlpha(36),
                      borderRadius: BorderRadius.circular(
                        20,
                      ), // More rounded for oval shape
                    ),
                    child: Text(
                      agent.isActive
                          ? UserStatus.active.value
                          : UserStatus.inactive.value,
                      style: AppFonts.mediumTextStyle(
                        12,
                        color: agent.isActive
                            ? AppTheme
                                  .statusActiveText // Darker green text
                            : AppTheme.statusInactiveText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                // Boolean flags to indicate which columns use widget builders
                useWidgetBuilders: [
                  false, // name
                  false, // contact
                  false, // email
                  false, // joinDate
                  false, // state
                  false, // city
                  false, // level
                  false, // commission
                  false, //associatedBroker
                  false, // referredBy
                  false, // totalAgents
                  false, // totalSales
                  false, // commission
                  true, // status
                ],
                actionBuilders: [
                  (context, agent) => ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isCompact: true,
                    isMobile: false,
                  ),
                ],
                mobileCardBuilder: (context, agent) =>
                  _buildMobileAgentCard(agent, context),
                onSort: handleSort,
                emptyStateMessage: noDataAvailable,
                pageCount: pageCount.value,
                isLoading: state is AgentLoading,
                totalElements: totalElements.value,
                onDateFilterChanged: (value) async {
                  selectedDate.value = value;
                  await _fetchAgents(
                    context,
                    user,
                    selectedDate: value,
                    page: currentpage.value,
                    searchString: searchString.value ?? "",
                  );
                },
                handleTableSearch: (value) async {
                  searchString.value = value;
                  await _fetchAgents(
                    context,
                    user,
                    selectedDate: selectedDate.value,
                    page: currentpage.value,
                    searchString: value,
                  );
                },
                handlePagination: (page) async {
                  currentpage.value = page;
                  await _fetchAgents(
                    context,
                    user,
                    selectedDate: selectedDate.value,
                    searchString: searchString.value ?? "",
                    page: page,
                  );
                },
                onAllFiltersChanged: (allFilters) async {
                  // Store current filters
                  currentFilters.value = allFilters;
                  String? relatedBrokerageValue;
                  String? agentNameValue;
                  // Extract dropdown filters
                  if (allFilters.containsKey(agentRelatedBrokerage)) {
                    relatedBrokerageValue = allFilters[agentRelatedBrokerage]
                        ?.toString();
                    relatedBrokerage.value = relatedBrokerageValue!;
                  }
                  if (allFilters.containsKey(agentName)) {
                    agentNameValue = allFilters[agentName]?.toString();
                    agentNameFilter.value = agentNameValue!;
                  }
                  //call api to filter the data
                  if (context.mounted && user != null) {
                    _fetchAgents(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      sortBy: sortByDefault,
                      sortDirection: "ASC",
                      associatedBrokerageId: relatedBrokerage.value,
                      agentId:agentNameFilter.value,
                      page: 0,
                      searchString: searchString.value ?? "",
                    );
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  _fetchAgents(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    String sortBy = sortByDefault,
    String sortDirection = "ASC",
    String associatedBrokerageId = "",
    String agentId = "",
    String active = "",  
    required int page,
    required String? searchString,
  }) {
    if (context.mounted) {
      String? formattedDate = selectedDate != null
          ? AppDateFormatter.formatDateyyyyMMdd(selectedDate)
          : null;

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortBy, // "created_at",
        "sortDirection": sortDirection,
        "searchString": searchString,
        "joiningDate": formattedDate,
        "associatedBrokerageId": associatedBrokerageId,
        "agentId":agentId,
        "userId": user?.userId,
      };

      context.read<AgentCubit>().getAgents(requestBody: payload);
    }
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    if (onNavigateToAgentNetworkAgent != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetworkAgent!(agent);
      } catch (e) {
        // If no matching broker found, show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Broker not found: ${agent.fullName}')),
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Action clicked for ${agent.fullName}')),
      );
    }
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                agent.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.isActive
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.isActive ? active : inactive,
                  style: AppFonts.normalTextStyle(
                    12,
                    color: agent.depthLevel == agentRoleValue
                        ? AppTheme
                              .agentStatusActiveText // Darker green text
                        : AppTheme.agentStatusInactiveText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.phone}'),
          Text('$agentEmail: ${agent.email}'),
          Text(
            '$agentJoinDate: ${AppDateFormatter.formatDateMMddyyyy(agent.joiningDate)}',
          ),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.depthLevel}'),
          Text('$agentRelatedBrokerage: ${agent.associatedBrokerage}'),
          Text('$agentRefferedBy: ${agent.recruiterName}'),
          Text('$agentDirectRecruits: ${agent.totalDownlineAgents}'),
          Text('$agentTotalSales: ${agent.totalSales}'),
          Text(
            '$agentTotalRevenue: $currencySymbol${agent.totalRevenue.toStringAsFixed(2)}',
          ),
          Text(
            '$agentCommission: $currencySymbol${agent.commission.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
