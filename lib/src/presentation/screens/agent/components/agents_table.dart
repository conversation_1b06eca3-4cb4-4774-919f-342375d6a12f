import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/column_mapping_utils.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../domain/models/user.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_theme.dart';
import '../../../../domain/models/agent_model.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsTable extends HookWidget {
  final bool showEditOptions;
  final bool showRecruits;
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;
  const AgentsTable({
    super.key,
    this.showEditOptions = false,
    this.showRecruits = false,
    required this.onNavigateToAgentNetworkAgent,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getAgentBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    // Get user from UserCubit
    final user = context.watch<UserCubit>().state.user;

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      //agent
      agentName,
      agentLevel,
      agentRefferedBy,
      agentJoinDate,
      agentState,
      agentCity,
      agentTotalSales,
      agentTotalRevenue,
      agentCommission,
    ];

    final sortColumn = useState<String>('');
    final sortOrder = useState<String>('');
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalElements = useState(0);
    final ValueNotifier<String?> searchString = useState(null);
    final ValueNotifier<DateTime?> selectedDate = useState(null);

    useEffect(() {
      Future.microtask(() async {
        if (context.mounted) {
          await _fetchAgents(
            context,
            user,
            selectedDate: selectedDate.value,
            searchString: searchString.value ?? "",
            page: currentpage.value,
          );
        }
      });
      return null;
    }, [user?.userId]);

    // Check if user is available
    if (user == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }
    return BlocConsumer<AgentCubit, AgentState>(
      listener: (context, state) {},
      builder: (context, state) {
        List<AgentModel> agentData = [];
        bool isLoading = false;
        String? errorMessage;

        if (state is AgentLoading) {
          isLoading = true;
        } else if (state is AgentLoaded) {
          agentData = state.agents;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.totalPages;
            totalElements.value = state.totalCount ?? 0;
          });
        } else if (state is AgentError) {
          errorMessage = state.message;
        }

        void handleSort(String columnName, bool ascending) async {
          // Map the display column name to the backend field name
          String backendColumnName = _getBackendColumnName(columnName);
          sortColumn.value = columnName;
          sortOrder.value = ascending ? 'ASC' : 'DESC';

          if (context.mounted) {
            await _fetchAgents(
              context,
              user,
              selectedDate: selectedDate.value,
              sortBy: backendColumnName,
              sortDirection: sortOrder.value,
              searchString: searchString.value ?? "",
              page: currentpage.value,
            );
          }
        }

        if (errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? Icons.lock_outline
                      : Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? authenticationRequired
                      : errorLoadingData,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? pleaseLoginToAccessAgentData
                      : '$errorPrefix$errorMessage',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    if (context.mounted) {
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        searchString: searchString.value ?? "",
                        page: currentpage.value,
                      );
                    }
                  },
                  child: const Text(retry),
                ),
              ],
            ),
          );
        }

        final isDesktop = Responsive.isDesktop(context);

        return LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //Customized table layout
                Flexible(
                  child: CustomDataTableWidget<AgentModel>(
                    data: agentData,
                    title: agents,
                    titleIcon: "$iconAssetpath/user.png",
                    searchHint: searchAgent,
                    searchFn: (agent) =>
                        agent.fullName +
                        agent.depthLevel +
                        AppDateFormatter.formatJoiningDate(agent.joiningDate) +
                        agent.recruiterName +
                        agent.state +
                        agent.city +
                        agent.totalSales.toString() +
                        agent.totalRevenue.toString() +
                        agent.commission.toString(),
                    // Dynamic filtering system
                    filterColumnNames: [
                      //agentName, // name
                      //agentState, // state
                      //agentRole, // role
                      agentJoinDate, // join date
                    ],
                    filterValueExtractors: {
                      //agentName: (agent) => agent.fullName,
                      //agentState: (agent) => agent.state,
                      //agentRole: (agent) => agent.level,
                      agentJoinDate: (agent) =>
                          agent.joiningDate.toIso8601String(),
                    },
                    dateFilterColumns: const [
                      agentJoinDate, // Join date should use calendar picker
                    ],
                    columnNames: formattedHeaders,
                    cellBuilders: [
                      (agent) => agent.fullName, // agentName
                      (agent) => agent.depthLevel, // agentLevel
                      (agent) => agent.recruiterName, // agentRefferedBy
                      (agent) =>
                          '${agent.joiningDate.day}/${agent.joiningDate.month}/${agent.joiningDate.year}', // agentJoinDate
                      (agent) => agent.state, // agentState
                      (agent) => agent.city, // agentCity
                      (agent) => agent.totalSales.toString(), // agentTotalSales
                      (agent) =>
                          '$currencySymbol${agent.totalRevenue.toStringAsFixed(2)}', // agentCommis// agentTotalRevenue
                      (agent) =>
                          '$currencySymbol${agent.commission.toStringAsFixed(2)}', // agentCommission
                    ],

                    /// to show icons before the cell content. eg: username and user icon
                    iconCellBuilders: [
                      (agent) => TableCellData(
                        text: agent.fullName,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 28,
                      ),
                      null, // level
                      (agent) => TableCellData(
                        text: agent.recruiterName,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 28,
                      ),
                      null, // joinDate
                      null, // state
                      null, // city
                      null, // totalSales
                      null, // totalRevenue
                      null, // commission
                    ],

                    /// Boolean flags to indicate which columns use icon cell builders. can enable/disable by setting this flag
                    useIconBuilders: [
                      true, // name - use icon
                      false, // level
                      true, // referredBy - use icon
                      false, // joinDate
                      false, // state
                      false, // city
                      false, // totalSales
                      false, // totalRevenue
                      false, // commission
                    ],

                    /// Widget builders for styled cells - none needed for this table
                    widgetCellBuilders: [
                      null, // name - use text
                      null, // level - use text
                      null, // joinDate - use text
                      null, // referredBy - use text
                      null, // totalSales - use text
                      null, // state - use text
                      null, // city - use text
                      null, // total revenue - use text
                      null, // commission - use text
                    ],
                    // Boolean flags to indicate which columns use widget builders
                    useWidgetBuilders: [
                      false, // name
                      false, // level
                      false, // referredBy
                      false, // joinDate
                      false, // state
                      false, // city
                      false, // totalSales
                      false, // totalRevenue
                      false, // commission
                    ],
                    actionBuilders: [
                      (context, agent) => ActionButtonEye(
                        onPressed: () => _onAgentAction(context, agent),
                        isCompact: true,
                        isMobile: false,
                      ),
                      if (showEditOptions) ...[
                        (context, agent) => ActionButtonEye(
                          onPressed: () => _onAgentAction(context, agent),
                          isCompact: true,
                          isMobile: false,
                          padding: 8,
                          icon: '$iconAssetpath/table_edit.png',
                        ),
                        (context, agent) => ActionButtonEye(
                          onPressed: () => _onAgentAction(context, agent),
                          isCompact: true,
                          isMobile: false,
                          padding: 8,
                          icon: '$iconAssetpath/delete.png',
                        ),
                      ],
                    ],
                    mobileCardBuilder: (context, agent) =>
                        _buildMobileAgentCard(agent, context),
                    onSort: handleSort,
                    emptyStateMessage: noDataAvailable,
                    useMinHeight: isDesktop,
                    minHeight: constraints.maxHeight,
                    pageCount: pageCount.value,
                    isLoading: state is AgentLoading,
                    totalElements: totalElements.value,
                    onDateFilterChanged: (value) async {
                      selectedDate.value = value;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: value,
                        page: currentpage.value,
                        searchString: searchString.value ?? "",
                      );
                    },
                    handleTableSearch: (value) async {
                      searchString.value = value;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        page: currentpage.value,
                        searchString: value,
                      );
                    },
                    handlePagination: (page) async {
                      currentpage.value = page;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        searchString: searchString.value ?? "",
                        page: page,
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
    // );
  }

  _fetchAgents(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    String sortBy = sortByDefault,
    String sortDirection = "ASC",
    required int page,
    required String? searchString,
  }) {
    if (context.mounted) {
      String? formattedDate = selectedDate != null
          ? AppDateFormatter.formatDateyyyyMMdd(selectedDate)
          : null;

      // TODO: update sortBy static value when server side updates
      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortBy, // "created_at",
        "sortDirection": sortDirection,
        "searchString": searchString,
        "joiningDate": formattedDate,
        "userId": user?.userId,
      };
      context.read<AgentCubit>().getAgents(requestBody: payload);
    }
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    // Navigate to agent detail or show action
    if (onNavigateToAgentNetworkAgent != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetworkAgent!(agent);
      } catch (e) {
        // If no matching broker found, show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Broker not found: ${agent.fullName}')),
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Action clicked forRR ${agent.fullName}')),
      );
    }
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(agent.fullName, style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text('$agentLevel: ${agent.depthLevel}'),
          Text(
            '$agentJoinDate: ${agent.joiningDate.day}/${agent.joiningDate.month}/${agent.joiningDate.year}',
          ),
          Text('$agentRefferedBy: ${agent.recruiterName}'),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),

          Text('$agentTotalSales: ${agent.totalSales}'),
          Text(
            '$agentEarning: $currencySymbol${agent.commission.toStringAsFixed(2)}',
          ),
          Text('$agentCommission: ${agent.commission}'),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
