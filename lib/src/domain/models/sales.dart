import 'dart:convert';
import 'package:intl/intl.dart';

import '../../core/utils/helper.dart';

SalesDetailsApi brokerApiFromJson(String str) =>
    SalesDetailsApi.fromJson(json.decode(str));
String brokerApiToJson(SalesDetailsApi data) => json.encode(data.toJson());

class SalesDetailsApi {
  List<Sales> sales;
  Pageable? pageable;
  int totalElements;
  int totalPages;
  bool last;
  int size;
  int number;
  SalesSort? sort;
  int numberOfElements;
  bool first;
  bool empty;

  SalesDetailsApi({
    required this.sales,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  SalesDetailsApi copyWith({
    List<Sales>? sales,
    Pageable? pageable,
    int? totalElements,
    int? totalPages,
    bool? last,
    int? size,
    int? number,
    SalesSort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,
  }) => SalesDetailsApi(
    sales: sales ?? this.sales,
    pageable: pageable ?? this.pageable,
    totalElements: totalElements ?? this.totalElements,
    totalPages: totalPages ?? this.totalPages,
    last: last ?? this.last,
    size: size ?? this.size,
    number: number ?? this.number,
    sort: sort ?? this.sort,
    numberOfElements: numberOfElements ?? this.numberOfElements,
    first: first ?? this.first,
    empty: empty ?? this.empty,
  );
  factory SalesDetailsApi.fromJson(Map<String, dynamic> json) =>
      SalesDetailsApi(
        sales: json["content"] != null
            ? List<Sales>.from(json["content"].map((x) => Sales.fromJson(x)))
            : [],
        pageable: Pageable.fromJson(json["pageable"]),
        totalElements: json["totalElements"] ?? 0,
        totalPages: json["totalPages"] ?? 0,
        last: json["last"] ?? false,
        size: json["size"] ?? 0,
        number: json["number"] ?? 0,
        sort: SalesSort.fromJson(json["sort"]),
        numberOfElements: json["numberOfElements"] ?? 0,
        first: json["first"] ?? false,
        empty: json["empty"] ?? false,
      );

  Map<String, dynamic> toJson() => {
    "content": List<dynamic>.from(sales.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "totalElements": totalElements,
    "totalPages": totalPages,
    "last": last,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "numberOfElements": numberOfElements,
    "first": first,
    "empty": empty,
  };
}

class Sales {
  final String transactionId;
  final String brokerName;
  final String agentName;
  final String propertyType;
  final String propertyAddress;
  final double propertyValue;
  final String representing;
  final String representingName;
  final String representingAddress;
  final DateTime listingDate;
  final DateTime saleDate;
  final double salePrice;
  final double commission;
  final double commissionAmt;

  Sales({
    required this.transactionId,
    required this.brokerName,
    required this.agentName,
    required this.propertyType,
    required this.propertyAddress,
    required this.propertyValue,
    required this.representing,
    required this.representingName,
    required this.representingAddress,
    required this.listingDate,
    required this.saleDate,
    required this.salePrice,
    required this.commission,
    required this.commissionAmt,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
      transactionId: json['transactionId']?.toString() ?? '',
      brokerName: json['brokerageName']?.toString() ?? '',
      agentName: json['agentName']?.toString() ?? '',
      propertyType: json['propertyType']?.toString() ?? '',
      propertyAddress: json['propertyAddress']?.toString() ?? '',
      propertyValue: toDouble(json['propertyValue']),
      representing: json['representing']?.toString() ?? '-',
      representingName: json['representingName']?.toString() ?? '-',
      representingAddress: json['representingAddress']?.toString() ?? '-',
      listingDate: DateFormat(
        "dd-MM-yyyy",
      ).parse(json["listingDate"] ?? DateTime.now().toString()),
      saleDate: DateFormat(
        "dd-MM-yyyy",
      ).parse(json["saleDate"] ?? DateTime.now().toString()),
      // listingDate: DateTime.parse(
      //   json["listingDate"] ?? DateTime.now().toString(),
      // ),
      // saleDate: DateTime.parse(json["saleDate"] ?? DateTime.now().toString()),
      salePrice: toDouble(json['salePrice']),
      commission: toDouble(json['commissionPercentage']),
      commissionAmt: toDouble(json['commissionAmount']),
    );
  }
  Map<String, dynamic> toJson() => {
    'transactionId': transactionId,
    'brokerageName': brokerName,
    'agentName': agentName,
    'propertyType': propertyType,
    'propertyAddress': propertyAddress,
    'propertyValue': propertyValue,
    "representing": representing,
    'buyerName': representingName,
    'buyerAddress': representingAddress,
    'listingDate': listingDate.toIso8601String(),
    'saleDate': saleDate.toIso8601String(),
    'salePrice': salePrice,
    'commissionPercentage': commission,
    'commissionAmount': commissionAmt,
  };
}

class Pageable {
  int pageNumber;
  int pageSize;
  SalesSort? sort;
  int offset;
  bool paged;
  bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  Pageable copyWith({
    int? pageNumber,
    int? pageSize,
    SalesSort? sort,
    int? offset,
    bool? paged,
    bool? unpaged,
  }) => Pageable(
    pageNumber: pageNumber ?? this.pageNumber,
    pageSize: pageSize ?? this.pageSize,
    sort: sort ?? this.sort,
    offset: offset ?? this.offset,
    paged: paged ?? this.paged,
    unpaged: unpaged ?? this.unpaged,
  );

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
    pageNumber: json["pageNumber"] ?? 0,
    pageSize: json["pageSize"] ?? 0,
    sort: SalesSort.fromJson(json["sort"]),
    offset: json["offset"] ?? 0,
    paged: json["paged"] ?? false,
    unpaged: json["unpaged"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "pageNumber": pageNumber,
    "pageSize": pageSize,
    "sort": sort?.toJson(),
    "offset": offset,
    "paged": paged,
    "unpaged": unpaged,
  };
}

class SalesSort {
  bool sorted;
  bool unsorted;
  bool empty;

  SalesSort({
    required this.sorted,
    required this.unsorted,
    required this.empty,
  });

  SalesSort copyWith({bool? sorted, bool? unsorted, bool? empty}) => SalesSort(
    sorted: sorted ?? this.sorted,
    unsorted: unsorted ?? this.unsorted,
    empty: empty ?? this.empty,
  );

  factory SalesSort.fromJson(Map<String, dynamic> json) => SalesSort(
    sorted: json["sorted"] ?? false,
    unsorted: json["unsorted"] ?? false,
    empty: json["empty"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "sorted": sorted,
    "unsorted": unsorted,
    "empty": empty,
  };
}
