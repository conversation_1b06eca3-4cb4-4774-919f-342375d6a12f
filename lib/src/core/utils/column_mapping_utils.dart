/// Utility class for mapping display column names to backend field names
/// This ensures consistent column mapping across different table components
/// for sorting and other operations.

import '/src/core/config/app_strings.dart';

class ColumnMappingUtils {
  /// Maps display column names to backend field names for broker-related tables
  /// This mapping is used for sorting operations to ensure the correct
  /// backend field names are sent to the API
  static const Map<String, String> _brokerColumnMapping = {
    // Basic broker information
    brokerListNameColumnHeader:
        'user_full_name', // 'Brokerage Name' -> 'user_full_name'
    brokerListContactColumnHeader: 'phone', // 'Contact' -> 'phone'
    brokerListEmailColumnHeader: 'email', // 'Email Address' -> 'email'
    // Date and location information
    brokerListJoinDateColumnHeader: 'created_at', // 'Join Date' -> 'created_at'
    brokerListStateColumnHeader: 'state', // 'State' -> 'state'
    brokerListCityColumnHeader: 'city', // 'City' -> 'city'
    brokerListAddressColumnHeader: 'address', // 'Address' -> 'address'
    // Performance metrics
    brokerListAgentsColumnHeader:
        'total_downline_agents', // 'Total Agents' -> 'total_downline_agents'
    brokerListTotalSalesColumnHeader:
        'total_sales', // 'Total Sales' -> 'total_sales'
    brokerListTotalRevenueColumnHeader:
        'total_revenue', // 'Total Revenue' -> 'total_revenue'
    brokerListCommissionColumnHeader:
        'commission', // 'Commission' -> 'commission'
    // Status and company information
    brokerListStatusColumnHeader: 'is_active', // 'Status' -> 'is_active'
    'Company Name': 'company_name', // 'Company Name' -> 'company_name'
  };

  /// Maps display column names to backend field names for sales-related tables
  /// This mapping is used for sorting operations to ensure the correct
  /// backend field names are sent to the API
  static const Map<String, String> _salesColumnMapping = {
    // Transaction and identification
    salesTransactionIdColumnHeader:
        'transaction_id', // 'Transaction ID' -> 'transaction_id'
    'ID': 'id', // 'ID' -> 'id'
    // User and agent information
    salesBrokerColumnHeader:
        'associated_brokerage_full_name', // 'Brokerage Name' -> 'associated_brokerage_full_name'
    salesAgentColumnHeader:
        'agent_full_name', // 'Agent Name' -> 'agent_full_name'
    'User Full Name': 'user_full_name', // 'User Full Name' -> 'user_full_name'
    'Recruiter Name':
        'recruiter_full_name', // 'Recruiter Name' -> 'recruiter_full_name'
    // Contact information
    'Email': 'email', // 'Email' -> 'email'
    // Property information
    salesPropertyTypeColumnHeader:
        'property_type_name', // 'Property Type' -> 'property_type_name'
    salesPropertyAddressColumnHeader:
        'property_address', // 'Property Address' -> 'property_address'
    salesPropertyValueColumnHeader:
        'property_value', // 'Property Value' -> 'property_value'
    // Transaction details
    representingColumnHeader:
        'representing', // 'Representing' -> 'representing_name'
    representingNameColumnHeader:
        'representing_name', // 'Representing Name' -> 'representing_name'
    representingAddressColumnHeader:
        'property_address', // 'Representing Address' -> 'property_address'
    'Transaction Type':
        'transaction_type_name', // 'Transaction Type' -> 'transaction_type_name'
    'Lead Source': 'lead_source_name', // 'Lead Source' -> 'lead_source_name'
    // Dates
    salesListingDateColumnHeader:
        'listing_date', // 'Listing Date' -> 'listing_date'
    salesDateColumnHeader: 'closing_date', // 'Sale Date' -> 'closing_date'
    'Created At': 'created_at', // 'Created At' -> 'created_at'
    // Financial information
    salesAmountColumnHeader: 'sale_price', // 'Sale Price' -> 'sale_price'
    salesCommissionColumnHeader:
        'commission_percentage', // 'Commission %' -> 'commission_percentage'
    salesCommissionAmtColumnHeader:
        'gross_commission', // 'Commission Amt' -> 'gross_commission'
    // Additional information
    'Buyer Info': 'buyer_info', // 'Buyer Info' -> 'buyer_info'
    'Seller Info': 'seller_info', // 'Seller Info' -> 'seller_info'
    // IDs for relationships
    'User ID': 'user_id', // 'User ID' -> 'user_id'
    'Agent ID': 'agent_id', // 'Agent ID' -> 'agent_id'
    'Organization ID':
        'organization_id', // 'Organization ID' -> 'organization_id'
    'Recruiter ID': 'recruiter_id', // 'Recruiter ID' -> 'recruiter_id'
  };

  static const Map<String, String> _agentColumnMapping = {
    // Basic agent information
    agentName: 'user_full_name',
    agentContact: 'phone',
    agentEmail: 'email',
    agentJoinDate: 'created_at',
    agentState: 'state',
    agentCity: 'city',
    agentLevel: 'depth_level',
    agentRelatedBrokerage: 'associated_brokerage_full_name',
    agentRefferedBy: 'recruiter_full_name',
    agentDirectRecruits: 'totalDownlineAgents',
    agentTotalSales: 'total_sales',
    agentTotalRevenue: 'total_revenue',
    agentCommission: 'commission',
    agentStatus: 'is_active',
  };

  /// Gets the backend field name for a given display column name
  /// Returns the original column name if no mapping is found
  ///
  /// Example:
  /// ```dart
  /// String backendField = ColumnMappingUtils.getBrokerBackendColumnName('Total Sales');
  /// // Returns: 'total_sales'
  /// ```
  static String getBrokerBackendColumnName(String displayColumnName) {
    return _brokerColumnMapping[displayColumnName] ?? displayColumnName;
  }

  /// Gets the display column name for a given backend field name
  /// Returns the original field name if no mapping is found
  ///
  /// Example:
  /// ```dart
  /// String displayName = ColumnMappingUtils.getBrokerDisplayColumnName('total_sales');
  /// // Returns: 'Total Sales'
  /// ```
  static String getBrokerDisplayColumnName(String backendFieldName) {
    for (var entry in _brokerColumnMapping.entries) {
      if (entry.value == backendFieldName) {
        return entry.key;
      }
    }
    return backendFieldName;
  }

  /// Gets all available broker column mappings
  /// Returns a copy of the mapping to prevent external modifications
  static Map<String, String> getAllBrokerColumnMappings() {
    return Map<String, String>.from(_brokerColumnMapping);
  }

  /// Validates if a display column name has a corresponding backend mapping
  static bool hasBrokerMapping(String displayColumnName) {
    return _brokerColumnMapping.containsKey(displayColumnName);
  }

  /// Validates if a backend field name has a corresponding display mapping
  static bool hasBackendMapping(String backendFieldName) {
    return _brokerColumnMapping.containsValue(backendFieldName);
  }

  /// Gets all available backend field names for broker tables
  static List<String> getAllBrokerBackendFields() {
    return _brokerColumnMapping.values.toList();
  }

  /// Gets all available display column names for broker tables
  static List<String> getAllBrokerDisplayColumns() {
    return _brokerColumnMapping.keys.toList();
  }

  // ========== SALES TABLE METHODS ==========

  /// Gets the backend field name for a given display column name (Sales)
  /// Returns the original column name if no mapping is found
  ///
  /// Example:
  /// ```dart
  /// String backendField = ColumnMappingUtils.getSalesBackendColumnName('Transaction ID');
  /// // Returns: 'transaction_id'
  /// ```
  static String getSalesBackendColumnName(String displayColumnName) {
    return _salesColumnMapping[displayColumnName] ?? displayColumnName;
  }

  /// Gets the display column name for a given backend field name (Sales)
  /// Returns the original field name if no mapping is found
  ///
  /// Example:
  /// ```dart
  /// String displayName = ColumnMappingUtils.getSalesDisplayColumnName('transaction_id');
  /// // Returns: 'Transaction ID'
  /// ```
  static String getSalesDisplayColumnName(String backendFieldName) {
    for (var entry in _salesColumnMapping.entries) {
      if (entry.value == backendFieldName) {
        return entry.key;
      }
    }
    return backendFieldName;
  }

  /// Gets all available sales column mappings
  /// Returns a copy of the mapping to prevent external modifications
  static Map<String, String> getAllSalesColumnMappings() {
    return Map<String, String>.from(_salesColumnMapping);
  }

  /// Validates if a display column name has a corresponding backend mapping (Sales)
  static bool hasSalesMapping(String displayColumnName) {
    return _salesColumnMapping.containsKey(displayColumnName);
  }

  /// Validates if a backend field name has a corresponding display mapping (Sales)
  static bool hasSalesBackendMapping(String backendFieldName) {
    return _salesColumnMapping.containsValue(backendFieldName);
  }

  /// Gets all available backend field names for sales tables
  static List<String> getAllSalesBackendFields() {
    return _salesColumnMapping.values.toList();
  }

  /// Gets all available display column names for sales tables
  static List<String> getAllSalesDisplayColumns() {
    return _salesColumnMapping.keys.toList();
  }

  static String getAgentBackendColumnName(String displayColumnName) {
    return _agentColumnMapping[displayColumnName] ?? displayColumnName;
  }
}
